<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShareFlix - Voice-Driven Investment Discovery</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body>
    <div class="container">
        <!-- Burger Menu -->
        <div class="burger-menu" id="burgerMenu">
            <div class="burger-icon">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <!-- Side Menu -->
        <div class="side-menu" id="sideMenu">
            <div class="side-menu-header">
                <h3>Menu</h3>
                <button class="close-menu" id="closeMenu">&times;</button>
            </div>
            <div class="side-menu-content">
                <button class="menu-item" id="menuHistoryBtn">
                    <i class="fas fa-history"></i>
                    <span>Conversation History</span>
                </button>
                <button class="menu-item" id="menuExportBtn">
                    <i class="fas fa-download"></i>
                    <span>Export Conversations</span>
                </button>
                <button class="menu-item" id="menuClearBtn">
                    <i class="fas fa-trash"></i>
                    <span>Clear History</span>
                </button>
                <button class="menu-item" id="menuFlipToggle">
                    <i class="fas fa-clone"></i>
                    <span>Flip Cards: <span id="flipCardsState">On</span></span>
                </button>
            </div>
        </div>

        <!-- Menu Overlay -->
        <div class="menu-overlay" id="menuOverlay"></div>


        <header class="header">
            <h1><i class="fas fa-circle"></i> ShareFlix</h1>
            <p class="subtitle">Voice-Driven Investment Discovery Platform</p>
        </header>

        <main class="main-content">
            <!-- Voice Interface Section -->
            <div class="voice-interface">
                <div class="conversation-display" id="conversationDisplay" style="position: relative;">
                    <div class="welcome-wrapper"
                        style="position:absolute;inset:0;display:flex;align-items:center;justify-content:center;text-align:center;pointer-events:none;">
                        <div class="welcome-message">
                            <i class="fas fa-robot"></i>
                            <p></p>
                        </div>
                    </div>
                </div>

                <!-- Predefined Prompts (Empty State) -->
                <div class="predefined-prompts" id="predefinedPrompts">
                    <div class="prompt-grid">
                        <button class="prompt-button" data-prompt="Give me a summary of today's market highlights and key insights from our investor network">
                            <i class="fas fa-chart-line"></i>
                            <span>Summary</span>
                        </button>
                        <button class="prompt-button" data-prompt="What are the latest club calls and investment discussions happening in our community?">
                            <i class="fas fa-users"></i>
                            <span>Club calls</span>
                        </button>
                        <button class="prompt-button" data-prompt="Show me this week's top performing stocks and trending investment themes">
                            <i class="fas fa-calendar-week"></i>
                            <span>Weekly</span>
                        </button>
                        <button class="prompt-button" data-prompt="I'd like to have a general conversation about investment opportunities">
                            <i class="fas fa-comments"></i>
                            <span>Chat</span>
                        </button>
                    </div>
                </div>

                <div class="voice-controls">
                    <div class="text-input-container">
                        <input type="text" class="text-input" id="textInput" placeholder="Chat with Echo">
                        <button class="send-button" id="sendButton">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                        <button class="mic-button" id="micButton">
                            <i class="fas fa-microphone" id="micIcon"></i>
                        </button>
                    </div>
                </div>

                <!-- Conversation History Modal -->
                <div class="history-modal" id="historyModal" style="display: none;">
                    <div class="history-modal-content">
                        <div class="history-header">
                            <h2>Conversation History</h2>
                            <button class="close-history" id="closeHistory">&times;</button>
                        </div>
                        <div class="history-content" id="historyContent">
                            <div class="loading">Loading conversations...</div>
                        </div>
                        <div class="history-actions">
                            <button class="export-btn" id="exportHistory">Export History</button>
                            <button class="clear-btn" id="clearHistory">Clear History</button>
                        </div>
                    </div>

                    <div class="audio-controls">
                        <button class="control-btn" id="playButton" disabled>
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="control-btn" id="oldStopButton" disabled>
                            <i class="fas fa-stop"></i>
                        </button>
                    </div>
                </div>

                <!-- AI Thinking Indicator -->
                <div class="ai-thinking-indicator" id="aiThinkingIndicator" style="display: none;">
                    <div class="thinking-animation">
                        <div class="typing-dots" aria-label="AI is thinking">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                    <span class="thinking-text">Sarah is thinking...</span>
                </div>



                <div class="status-indicator" id="statusIndicator">
                    <span class="status-text">Start typing or speaking...</span>
                </div>

            </div>

            <!-- Lead Information Panel -->
            <div class="lead-panel" id="leadPanel">
                <h3><i class="fas fa-user-plus"></i> Lead Information</h3>
                <div class="lead-info" id="leadInfo">
                    <p class="info-placeholder">Lead information will appear here as the conversation progresses...</p>
                </div>

                <div class="lead-actions">
                    <button class="action-btn save-btn" id="saveLeadBtn" disabled>
                        <i class="fas fa-save"></i> Save Lead
                    </button>
                    <button class="action-btn export-btn" id="exportLeadBtn" disabled>
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
        </main>

    </div>

    <!-- Audio element for playback -->
    <audio id="audioPlayer" controls style="display: none;"></audio>

    <!-- Dedicated audio element for welcome message -->
    <audio id="welcomeAudioPlayer" preload="none" style="display: none;"></audio>

    <script src="app.js"></script>
</body>

</html>